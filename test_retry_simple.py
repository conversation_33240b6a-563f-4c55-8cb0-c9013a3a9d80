#!/usr/bin/env python3

"""
Teste simples para verificar se a implementação de retry com tenacity está funcionando
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# <PERSON>iro, vamos testar se conseguimos importar
try:
    from trader.mercado_bitcoin_api import MercadoBitcoinAPI, UnauthorizedError
    print("✅ Importação bem-sucedida!")
except Exception as e:
    print(f"❌ Erro na importação: {e}")
    sys.exit(1)

def test_retry_functionality():
    """Teste simples da funcionalidade de retry"""
    print("🧪 Testando funcionalidade de retry com tenacity...")
    
    with patch.object(MercadoBitcoinAPI, '_authorize') as mock_authorize:
        # Cria instância da API
        api = MercadoBitcoinAPI("test_key", "test_secret")
        
        # Mock do session.request
        api.session.request = Mock()
        
        # Primeira chamada retorna 401, segunda retorna sucesso
        mock_response_401 = Mock()
        mock_response_401.status_code = 401
        mock_response_401.text = "Unauthorized"
        
        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = {"success": True}
        
        api.session.request.side_effect = [mock_response_401, mock_response_success]
        
        try:
            # Executa o método que deve fazer retry
            result = api._make_request("GET", "/test")
            
            print("✅ Retry funcionou! Resultado:", result)
            print(f"✅ Re-autenticação foi chamada: {mock_authorize.called}")
            print(f"✅ Número de requests feitos: {api.session.request.call_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro no teste: {e}")
            return False

def test_max_retries():
    """Teste de esgotamento de tentativas"""
    print("\n🧪 Testando esgotamento de tentativas...")
    
    with patch.object(MercadoBitcoinAPI, '_authorize') as mock_authorize:
        # Cria instância da API
        api = MercadoBitcoinAPI("test_key", "test_secret")
        
        # Mock do session.request
        api.session.request = Mock()
        
        # Todas as chamadas retornam 401
        mock_response_401 = Mock()
        mock_response_401.status_code = 401
        mock_response_401.text = "Unauthorized"
        
        api.session.request.return_value = mock_response_401
        
        try:
            # Executa e espera exceção
            result = api._make_request("GET", "/test")
            print("❌ Deveria ter lançado exceção!")
            return False
            
        except UnauthorizedError:
            print("✅ Exceção UnauthorizedError lançada corretamente")
            print(f"✅ Re-autenticação foi chamada {mock_authorize.call_count} vezes")
            return True
            
        except Exception as e:
            print(f"❌ Exceção inesperada: {e}")
            return False

if __name__ == "__main__":
    print("🚀 Iniciando testes de retry com tenacity...\n")
    
    success1 = test_retry_functionality()
    success2 = test_max_retries()
    
    if success1 and success2:
        print("\n🎉 Todos os testes passaram! A implementação com tenacity está funcionando.")
    else:
        print("\n❌ Alguns testes falharam.")
        sys.exit(1)
