# Trading Bot - Mercado Bitcoin

Bot de trading automatizado para a API do Mercado Bitcoin.

## Instalação

Este projeto usa [UV](https://docs.astral.sh/uv/) como gerenciador de dependências.

### Instalar UV

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### Configurar projeto

```bash
# Instalar dependências
uv sync

# Instalar dependências de desenvolvimento
uv sync --extra dev
```

## Configuração

1. Copie o arquivo de exemplo:
```bash
cp .env.example .env
```

2. Configure suas credenciais da API do Mercado Bitcoin no arquivo `.env`:
```
MB_API_KEY=sua_chave_api_aqui
MB_API_SECRET=seu_secret_aqui
```

## Uso

```bash
# Executar o bot
uv run python main.py

# Executar testes
uv run pytest

# Executar testes com cobertura
uv run pytest --cov=.
```

## Estrutura

- `mercado_bitcoin_api.py` - Cliente da API do Mercado Bitcoin
- `trading_strategy.py` - Estratégias de trading
- `bot.py` - Bot principal
- `main.py` - Ponto de entrada
- `test_*.py` - Testes unitários

## Estratégias

O bot inclui uma estratégia de média móvel simples. Você pode criar novas estratégias implementando a classe `TradingStrategy`.