from abc import ABC, abstractmethod
from decimal import Decimal
from typing import Dict, Any
import logging
from .account import Position


class TradingStrategy(ABC):
    """Classe base para estratégias de trading"""

    @abstractmethod
    def should_buy(self, market_price: Decimal) -> bool:
        pass

    @abstractmethod
    def should_sell(self, market_price: Decimal, position: Position) -> bool:
        pass

    @abstractmethod
    def calculate_quantity(self, balance: Decimal, price: Decimal) -> str:
        pass

    @abstractmethod
    def update_price(self, price: Decimal):
        pass


class SimpleMovingAverageStrategy(TradingStrategy):
    """Estratégia baseada em média móvel simples"""

    def __init__(self, short_period: int = 10, long_period: int = 30):
        self.short_period = short_period
        self.long_period = long_period
        self.price_history = []

    def update_price(self, price: Decimal):
        """Atualiza histórico de preços"""
        self.price_history.append(price)
        if len(self.price_history) > self.long_period:
            self.price_history.pop(0)

    def _calculate_sma(self, period: int) -> Decimal:
        """Calcula média móvel simples"""
        if len(self.price_history) < period:
            return 0
        return sum(self.price_history[-period:]) / period

    def should_buy(self, market_price: Decimal) -> bool:
        """Compra quando SMA curta cruza acima da SMA longa"""
        if len(self.price_history) < self.long_period:
            return False

        short_sma = self._calculate_sma(self.short_period)
        long_sma = self._calculate_sma(self.long_period)

        return short_sma > long_sma

    def should_sell(self, market_price: Decimal, position: Position) -> bool:
        """Vende quando SMA curta cruza abaixo da SMA longa"""
        if len(self.price_history) < self.long_period:
            return False

        short_sma = self._calculate_sma(self.short_period)
        long_sma = self._calculate_sma(self.long_period)

        return short_sma < long_sma

    def calculate_quantity(self, balance: Decimal, price: Decimal) -> str:
        """Calcula quantidade baseada em 10% do saldo"""
        quantity = (balance * 0.1) / price
        return f"{quantity:.8f}"


class MinhaStrategy(TradingStrategy):
    """Estratégia"""

    def __init__(
        self,
        percentual_stop_loss: Decimal = Decimal("0.10"),
        percentual_gain_treshold: Decimal = Decimal("0.30"),
        hard_stop_loss: Decimal = Decimal("0.0"),
        hard_gain_treshold: Decimal = Decimal("0.0"),
    ):
        self.percentual_stop_loss = percentual_stop_loss
        self.percentual_gain_treshold = percentual_gain_treshold
        self.hard_stop_loss = hard_stop_loss
        self.hard_gain_treshold = hard_gain_treshold
        self.price_history = []
        self.price_lock: Decimal = 0
        self.position_price_lock: Decimal = 0

    def from_percentual_of_position(
        cls,
        percentual_stop_loss: Decimal = Decimal("0.10"),
        percentual_gain_treshold: Decimal = Decimal("0.30"),
    ):
        return cls(
            percentual_stop_loss=percentual_stop_loss,
            percentual_gain_treshold=percentual_gain_treshold,
            hard_stop_loss=None,
            hard_gain_treshold=None,
        )

    @classmethod
    def from_hard_price(
        cls, hard_stop_loss: Decimal = 0.10, hard_gain_treshold: Decimal = 0.30
    ):
        return cls(
            percentual_stop_loss=None,
            percentual_gain_treshold=None,
            hard_stop_loss=hard_stop_loss,
            hard_gain_treshold=hard_gain_treshold,
        )

    def price_stop_loss(self) -> Decimal:
        if self.hard_stop_loss:
            return self.price_lock - self.hard_stop_loss
        return self.price_lock * Decimal(1 - self.percentual_stop_loss)

    def price_gain_treshold(self) -> Decimal:
        if self.hard_gain_treshold:
            return self.price_lock + self.hard_gain_treshold
        return self.price_lock * Decimal(1 + self.percentual_gain_treshold)

    def update_price(self, price: Decimal, position: Position):
        """Atualiza histórico de preços"""
        self.price_history.append(price)
        if price >= self.price_gain_treshold():
            self.price_lock = price
        if position.is_open():
            self.position_price_lock = price

    def should_buy(self, market_price: Decimal) -> bool:
        _should_buy = True
        # _should_buy = self.price_lock == market_data["price"]
        return _should_buy

    def should_sell(self, market_price: Decimal, position: Position) -> bool:
        _should_sell = market_price < self.price_stop_loss()
        return _should_sell

    def calculate_quantity(self, balance: Decimal, price: Decimal) -> str:
        quantity = (balance * Decimal("0.8")) / price
        return f"{quantity:.8f}"

    def __str__(self):
        return f"MinhaStrategy(price_stop_loss={self.price_stop_loss()}, price_gain_treshold={self.price_gain_treshold()})"
