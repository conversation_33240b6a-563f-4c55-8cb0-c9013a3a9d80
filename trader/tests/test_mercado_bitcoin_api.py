import pytest
from unittest.mock import Mock, patch, MagicMock
import json
import time
from trader.mercado_bitcoin_api import MercadoBitcoinAPI, UnauthorizedError

class TestMercadoBitcoinAPI:
    
    @pytest.fixture
    def api(self):
        """Instância da API para testes"""
        with patch.object(MercadoBitcoinAPI, '_authorize'):
            return MercadoBitcoinAPI("test_key", "test_secret")
    
    def test_init(self):
        """Testa inicialização da API"""
        with patch.object(MercadoBitcoinAPI, '_authorize'):
            api = MercadoBitcoinAPI("my_key", "my_secret")

            assert api.api_key == "my_key"
            assert api.api_secret == "my_secret"
            assert api.base_url == "https://api.mercadobitcoin.net/api/v4"
    
    @patch('time.time')
    def test_generate_signature(self, mock_time, api):
        """Testa geração de assinatura"""
        mock_time.return_value = 1640995200.123  # Timestamp fixo
        
        timestamp, signature = api._generate_signature("GET", "/api/v4/test", "")
        
        assert timestamp == "1640995200123"
        assert isinstance(signature, str)
        assert len(signature) == 64  # SHA256 hex
    
    @patch('requests.request')
    def test_make_request_get(self, mock_request, api):
        """Testa requisição GET"""
        mock_response = Mock()
        mock_response.json.return_value = {"success": True}
        mock_request.return_value = mock_response
        
        result = api._make_request("GET", "/test")
        
        assert result == {"success": True}
        mock_request.assert_called_once()
        
        # Verificar headers
        call_args = mock_request.call_args
        headers = call_args[1]['headers']
        assert "MB-ACCESS-KEY" in headers
        assert "MB-ACCESS-TIMESTAMP" in headers
        assert "MB-ACCESS-SIGNATURE" in headers
    
    @patch('requests.request')
    def test_make_request_post_with_data(self, mock_request, api):
        """Testa requisição POST com dados"""
        mock_response = Mock()
        mock_response.json.return_value = {"order_id": "123"}
        mock_request.return_value = mock_response
        
        data = {"symbol": "BTC-BRL", "side": "buy"}
        result = api._make_request("POST", "/orders", data)
        
        assert result == {"order_id": "123"}
        
        # Verificar que os dados foram enviados como JSON
        call_args = mock_request.call_args
        assert call_args[1]['data'] == json.dumps(data)
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_account_balance(self, mock_request, api):
        """Testa obtenção de saldo"""
        mock_request.return_value = {"accounts": []}
        
        result = api.get_account_balance()
        
        mock_request.assert_called_once_with("GET", "/accounts/balance")
        assert result == {"accounts": []}
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_ticker(self, mock_request, api):
        """Testa obtenção de ticker"""
        mock_request.return_value = {"last": "50000.00"}
        
        result = api.get_ticker("BTC-BRL")
        
        mock_request.assert_called_once_with("GET", "/ticker?symbols=BTC-BRL")
        assert result == {"last": "50000.00"}
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_place_order_market(self, mock_request, api):
        """Testa colocação de ordem market"""
        mock_request.return_value = {"id": "12345"}
        
        result = api.place_order("BTC-BRL", "buy", "market", "0.001")
        
        expected_data = {
            "symbol": "BTC-BRL",
            "side": "buy",
            "type": "market",
            "quantity": "0.001"
        }
        mock_request.assert_called_once_with("POST", "/orders", expected_data)
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_place_order_limit(self, mock_request, api):
        """Testa colocação de ordem limit"""
        mock_request.return_value = {"id": "12345"}
        
        result = api.place_order("BTC-BRL", "sell", "limit", "0.001", "50000.00")
        
        expected_data = {
            "symbol": "BTC-BRL",
            "side": "sell",
            "type": "limit",
            "quantity": "0.001",
            "price": "50000.00"
        }
        mock_request.assert_called_once_with("POST", "/orders", expected_data)
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_orders_no_params(self, mock_request, api):
        """Testa listagem de ordens sem parâmetros"""
        mock_request.return_value = {"orders": []}
        
        result = api.get_orders()
        
        mock_request.assert_called_once_with("GET", "/orders")
    
    @patch.object(MercadoBitcoinAPI, '_make_request')
    def test_get_orders_with_params(self, mock_request, api):
        """Testa listagem de ordens com parâmetros"""
        mock_request.return_value = {"orders": []}

        result = api.get_orders(symbol="BTC-BRL", status="open")

        mock_request.assert_called_once_with("GET", "/orders?symbol=BTC-BRL&status=open")

    @patch.object(MercadoBitcoinAPI, '_authorize')
    def test_retry_on_401_success(self, mock_authorize, api):
        """Testa retry bem-sucedido após erro 401"""
        # Simula primeiro request falhando com 401, segundo sucedendo
        api.session.request = Mock()

        # Primeira chamada retorna 401
        mock_response_401 = Mock()
        mock_response_401.status_code = 401
        mock_response_401.text = "Unauthorized"

        # Segunda chamada retorna sucesso
        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = {"success": True}

        api.session.request.side_effect = [mock_response_401, mock_response_success]

        # Executa o método que deve fazer retry
        result = api._make_request("GET", "/test")

        # Verifica se re-autenticou
        mock_authorize.assert_called()

        # Verifica resultado
        assert result == {"success": True}

        # Verifica que fez 2 requests (primeiro falhou, segundo sucedeu)
        assert api.session.request.call_count == 2

    @patch.object(MercadoBitcoinAPI, '_authorize')
    def test_retry_on_401_max_retries_exceeded(self, mock_authorize, api):
        """Testa falha após esgotar tentativas de retry"""
        api.session.request = Mock()

        # Todas as chamadas retornam 401
        mock_response_401 = Mock()
        mock_response_401.status_code = 401
        mock_response_401.text = "Unauthorized"

        api.session.request.return_value = mock_response_401

        # Executa e espera exceção
        with pytest.raises(UnauthorizedError):
            api._make_request("GET", "/test")

        # Verifica que tentou re-autenticar 3 vezes (padrão do tenacity)
        assert mock_authorize.call_count == 3

    @patch.object(MercadoBitcoinAPI, '_authorize')
    def test_no_retry_on_non_401_error(self, mock_authorize, api):
        """Testa que não faz retry para erros diferentes de 401"""
        api.session.request = Mock()

        # Simula erro 500 (não deve fazer retry)
        mock_response_500 = Mock()
        mock_response_500.status_code = 500
        mock_response_500.text = "Internal Server Error"

        api.session.request.return_value = mock_response_500

        # Executa e espera exceção imediata
        with pytest.raises(Exception) as exc_info:
            api._make_request("GET", "/test")

        # Verifica que NÃO tentou re-autenticar
        mock_authorize.assert_not_called()

        # Verifica que fez apenas 1 request
        assert api.session.request.call_count == 1

        # Verifica que a exceção contém o erro 500
        assert "500" in str(exc_info.value)

    def test_unauthorized_error_creation(self):
        """Testa criação da exceção UnauthorizedError"""
        error = UnauthorizedError("Token expirado")
        assert str(error) == "Token expirado"
        assert isinstance(error, Exception)