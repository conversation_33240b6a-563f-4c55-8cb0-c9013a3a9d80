#!/usr/bin/env python3
"""
Exemplo de uso do sistema de logging colorido
"""

import time
from decimal import Decimal
from trader.colored_logger import get_trading_logger, setup_colored_logging

def demo_colored_logging():
    """Demonstra o sistema de logging colorido"""
    
    print("🎨 Demonstração do Sistema de Logging Colorido\n")
    
    # Método 1: Usando TradingLogger diretamente
    print("=== Método 1: TradingLogger ===")
    trading_logger = get_trading_logger("Demo")
    
    # Logs específicos para trading
    trading_logger.log_bot_start("BTC-BRL")
    trading_logger.log_balance(1000.50, 0.00123456)
    trading_logger.log_price("BTC-BRL", 651244.00)
    
    trading_logger.log_buy_signal(651244.00)
    trading_logger.log_buy_order(651244.00, 0.00123456)
    trading_logger.log_position("long", 0.00123456, 651244.00)
    
    time.sleep(1)
    
    trading_logger.log_sell_signal(655000.00)
    trading_logger.log_sell_order(655000.00, 0.00123456)
    trading_logger.log_pnl(4.63, 4.63)  # PnL positivo
    
    trading_logger.log_error("Exemplo de erro", Exception("Conexão perdida"))
    trading_logger.log_warning("Saldo baixo detectado")
    
    trading_logger.log_bot_stop()
    
    print("\n=== Método 2: Logger tradicional com cores automáticas ===")
    
    # Método 2: Usando logger tradicional com colorização automática
    logger = setup_colored_logging("AutoDemo")
    
    # O formatter detecta automaticamente o tipo de mensagem e aplica cores
    logger.info("Preço atual BTC-BRL: R$ 651,244.00")
    logger.info("Sinal de compra detectado")
    logger.info("Ordem de compra executada com sucesso")
    logger.info("Posição atual: LONG 0.00123456 @ R$ 651,244.00")
    logger.info("Lucro não realizado: R$ 4.63")
    logger.warning("Saldo BRL baixo: R$ 45.32")
    logger.error("Erro na conexão com a API")
    logger.info("Sinal de venda detectado")
    logger.info("Ordem de venda executada")
    logger.info("Lucro total realizado: R$ 4.63")
    
    print("\n=== Método 3: Diferentes níveis de log ===")
    
    logger.debug("Debug: Verificando conexão...")
    logger.info("Info: Sistema funcionando normalmente")
    logger.warning("Warning: Volatilidade alta detectada")
    logger.error("Error: Falha na execução da ordem")
    logger.critical("Critical: Sistema fora do ar!")
    
    print("\n=== Cores por Categoria ===")
    print("💰 Preços: Azul brilhante")
    print("📈 Compras: Verde brilhante") 
    print("📉 Vendas: Vermelho brilhante")
    print("💰 Lucros: Verde")
    print("💸 Prejuízos: Vermelho")
    print("📊 Posições: Magenta")
    print("💳 Saldos: Ciano")
    print("⚡ Sinais: Amarelo brilhante")
    print("✅ Ordens: Branco brilhante")
    print("❌ Erros: Vermelho brilhante")
    print("⚠️ Avisos: Amarelo")
    print("ℹ️ Informações: Branco")

def demo_trading_scenario():
    """Simula um cenário completo de trading com logs coloridos"""
    
    print("\n🚀 Simulação de Cenário de Trading\n")
    
    trading_logger = get_trading_logger("TradingSimulation")
    
    # Início do bot
    trading_logger.log_bot_start("BTC-BRL")
    trading_logger.log_balance(5000.00, 0.0)
    
    # Simulação de preços e operações
    prices = [650000, 651000, 652000, 651500, 653000, 654000, 652000, 655000]
    
    position_open = False
    entry_price = 0
    quantity = 0
    
    for i, price in enumerate(prices):
        trading_logger.log_price("BTC-BRL", price)
        
        # Simular estratégia simples
        if not position_open and i > 2:  # Comprar após algumas observações
            trading_logger.log_buy_signal(price)
            quantity = 5000 / price  # Usar todo o saldo
            trading_logger.log_buy_order(price, quantity)
            trading_logger.log_position("long", quantity, price)
            position_open = True
            entry_price = price
            
        elif position_open and price > entry_price * 1.005:  # Vender com 0.5% de lucro
            trading_logger.log_sell_signal(price)
            trading_logger.log_sell_order(price, quantity)
            
            realized_pnl = (price - entry_price) * quantity
            trading_logger.log_pnl(0, realized_pnl)
            
            position_open = False
            
        elif position_open:
            # Atualizar PnL não realizado
            unrealized_pnl = (price - entry_price) * quantity
            trading_logger.log_pnl(unrealized_pnl, 0)
        
        time.sleep(0.5)  # Simular intervalo entre atualizações
    
    trading_logger.log_bot_stop()

if __name__ == "__main__":
    try:
        demo_colored_logging()
        demo_trading_scenario()
        
        print("\n✨ Demonstração concluída!")
        print("💡 Dica: Execute este script para ver as cores em ação no terminal!")
        
    except KeyboardInterrupt:
        print("\n🛑 Demonstração interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro na demonstração: {e}")
