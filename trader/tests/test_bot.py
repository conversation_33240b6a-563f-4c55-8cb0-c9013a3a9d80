import pytest
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal
from trader.bot import TradingBot
from trader.mercado_bitcoin_api import MercadoBitcoinAPI, TickerData, AccountData, AccountBalanceData
from trader.trading_strategy import TradingStrategy


class TestTradingBot:

    @pytest.fixture
    def mock_api(self):
        """Mock da API do Mercado Bitcoin"""
        api = Mock(spec=MercadoBitcoinAPI)

        # Mock do ticker retornando TickerData
        ticker_data = TickerData(
            buy=Decimal("49500.00"),
            date=**********,
            high=Decimal("51000.00"),
            last=Decimal("50000.00"),
            low=Decimal("49000.00"),
            open=Decimal("49800.00"),
            pair="BTC-BRL",
            sell=Decimal("50500.00"),
            vol=Decimal("100.5")
        )
        api.get_ticker.return_value = ticker_data

        # Mock das contas
        brl_account = AccountData(
            currency="BRL",
            currencySign="R$",
            id="brl-account-id",
            name="Real Account",
            type="spot"
        )
        btc_account = AccountData(
            currency="BTC",
            currencySign="₿",
            id="btc-account-id",
            name="Bitcoin Account",
            type="spot"
        )
        api.get_accounts.return_value = [brl_account, btc_account]

        # Mock dos saldos
        def mock_get_account_balance(account_id):
            if account_id == "brl-account-id":
                return [AccountBalanceData(
                    available=Decimal("1000.00"),
                    on_hold=Decimal("0.00"),
                    symbol="BRL",
                    total=Decimal("1000.00")
                )]
            elif account_id == "btc-account-id":
                return [AccountBalanceData(
                    available=Decimal("0.01"),
                    on_hold=Decimal("0.00"),
                    symbol="BTC",
                    total=Decimal("0.01")
                )]
            return []

        api.get_account_balance.side_effect = mock_get_account_balance
        api.place_order.return_value = {"id": "12345", "status": "filled"}
        return api

    @pytest.fixture
    def mock_strategy(self):
        """Mock da estratégia de trading"""
        strategy = Mock(spec=TradingStrategy)
        strategy.should_buy.return_value = False
        strategy.should_sell.return_value = False
        strategy.calculate_quantity.return_value = "0.001"
        strategy.update_price_history.return_value = None
        return strategy

    @pytest.fixture
    def bot(self, mock_api, mock_strategy):
        """Instância do bot para testes"""
        return TradingBot(mock_api, mock_strategy, "BTC-BRL")

    def test_init(self, mock_api, mock_strategy):
        """Testa inicialização do bot"""
        bot = TradingBot(mock_api, mock_strategy, "ETH-BRL")

        assert bot.api == mock_api
        assert bot.strategy == mock_strategy
        assert bot.symbol == "ETH-BRL"
        assert bot.is_running is False
        assert bot.position is None

    def test_get_current_price(self, bot, mock_api):
        """Testa obtenção do preço atual"""
        ticker_data = TickerData(
            buy=Decimal("44500.00"),
            date=**********,
            high=Decimal("46000.00"),
            last=Decimal("45000.50"),
            low=Decimal("44000.00"),
            open=Decimal("44800.00"),
            pair="BTC-BRL",
            sell=Decimal("45500.00"),
            vol=Decimal("100.5")
        )
        mock_api.get_ticker.return_value = ticker_data

        price = bot.get_current_price()

        assert price == 45000.50
        mock_api.get_ticker.assert_called_once_with("BTC-BRL")

    def test_get_balance_brl(self, bot, mock_api):
        """Testa obtenção de saldo em BRL"""
        balance = bot.get_balance("BRL")

        assert balance == Decimal("1000.00")
        mock_api.get_accounts.assert_called_once()
        mock_api.get_account_balance.assert_called_with("brl-account-id")

    def test_get_balance_btc(self, bot, mock_api):
        """Testa obtenção de saldo em BTC"""
        balance = bot.get_balance("BTC")

        assert balance == Decimal("0.01")

    def test_get_balance_not_found(self, bot, mock_api):
        """Testa saldo de moeda não encontrada"""
        balance = bot.get_balance("ETH")

        assert balance == Decimal("0.0")

    def test_execute_buy_order_success(self, bot, mock_api, mock_strategy):
        """Testa execução bem-sucedida de ordem de compra"""
        mock_strategy.calculate_quantity.return_value = "0.002"

        bot.execute_buy_order(50000.0)

        mock_api.place_order.assert_called_once_with(
            symbol="BTC-BRL", side="buy", type_order="market", quantity="0.002"
        )
        assert bot.position == "long"

    def test_execute_buy_order_insufficient_balance(self, bot, mock_api):
        """Testa compra com saldo insuficiente"""
        # Configurar mock para retornar saldo baixo
        def mock_get_account_balance_low(account_id):
            if account_id == "brl-account-id":
                return [AccountBalanceData(
                    available=Decimal("30.00"),
                    on_hold=Decimal("0.00"),
                    symbol="BRL",
                    total=Decimal("30.00")
                )]
            return []

        mock_api.get_account_balance.side_effect = mock_get_account_balance_low

        with patch.object(bot.logger, "warning") as mock_warning:
            bot.execute_buy_order(50000.0)
            mock_warning.assert_called_with("Saldo insuficiente para compra")

        mock_api.place_order.assert_not_called()

    def test_execute_sell_order_success(self, bot, mock_api):
        """Testa execução bem-sucedida de ordem de venda"""
        bot.execute_sell_order()

        mock_api.place_order.assert_called_once_with(
            symbol="BTC-BRL", side="sell", type_order="market", quantity="0.********"
        )
        assert bot.position is None

    def test_execute_sell_order_no_btc(self, bot, mock_api):
        """Testa venda sem BTC"""
        mock_api.get_account_balance.return_value = {
            "accounts": [{"currency": "BTC", "available": "0.000001"}]
        }

        with patch.object(bot.logger, "warning") as mock_warning:
            bot.execute_sell_order()
            mock_warning.assert_called_with("Sem BTC para vender")

        mock_api.place_order.assert_not_called()

    @patch("time.sleep")
    def test_run_buy_signal(self, mock_sleep, bot, mock_strategy):
        """Testa execução do bot com sinal de compra"""
        mock_strategy.should_buy.return_value = True
        bot.is_running = True

        # Simular uma iteração e parar
        def stop_after_one(interval):
            bot.stop()

        mock_sleep.side_effect = stop_after_one

        with patch.object(bot, "execute_buy_order") as mock_buy:
            bot.run(interval=1)
            mock_buy.assert_called_once()

    @patch("time.sleep")
    def test_run_sell_signal(self, mock_sleep, bot, mock_strategy):
        """Testa execução do bot com sinal de venda"""
        bot.position = "long"
        mock_strategy.should_sell.return_value = True
        bot.is_running = True

        def stop_after_one(interval):
            bot.stop()

        mock_sleep.side_effect = stop_after_one

        with patch.object(bot, "execute_sell_order") as mock_sell:
            bot.run(interval=1)
            mock_sell.assert_called_once()

    def test_stop(self, bot):
        """Testa parada do bot"""
        bot.is_running = True
        bot.stop()

        assert bot.is_running is False
