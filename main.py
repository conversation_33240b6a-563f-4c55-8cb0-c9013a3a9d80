from trader.mercado_bitcoin_api import MercadoBitcoinAPI
from trader.trading_strategy import MinhaStrategy
from trader.bot import TradingBot
from trader.account import Account
import os
from dotenv import load_dotenv
from decimal import Decimal


def main():
    # Carregar variáveis do arquivo .env
    load_dotenv()

    # Configurar credenciais (use variáveis de ambiente)
    api_key = os.getenv("MB_API_KEY")
    api_secret = os.getenv("MB_API_SECRET")

    if not api_key or not api_secret:
        print("Configure as variáveis MB_API_KEY e MB_API_SECRET")
        return

    # Inicializar API
    api = MercadoBitcoinAPI(api_key, api_secret)

    # Configurar estratégia
    strategy = MinhaStrategy.from_percentual_of_position(percentual_stop_loss=Decimal("0.10"), percentual_gain_treshold=Decimal("0.10"))

    # Configurar conta
    account = Account(api, "BTC-BRL")

    # Criar e executar bot
    bot = TradingBot(api, strategy, account)

    try:
        bot.run(interval=60)
    except KeyboardInterrupt:
        bot.stop()


if __name__ == "__main__":
    main()
