import unittest
from unittest.mock import Mock, patch
from decimal import Decimal
from datetime import datetime
from trader.bot import Account, Position, PositionHistory
from trader.mercado_bitcoin_api import MercadoBitcoinAPI, AccountData, AccountBalanceData


class TestAccount(unittest.TestCase):
    def setUp(self):
        """Configuração inicial para os testes"""
        self.mock_api = Mock(spec=MercadoBitcoinAPI)
        self.account = Account(self.mock_api, "BTC-BRL")
        
        # Mock dos dados de conta
        self.mock_account_data = AccountData(
            currency="BRL",
            currencySign="R$",
            id="brl-account-id",
            name="Real Account",
            type="spot"
        )
        
        self.mock_btc_account_data = AccountData(
            currency="BTC",
            currencySign="₿",
            id="btc-account-id",
            name="Bitcoin Account",
            type="spot"
        )
        
        # Mock dos dados de saldo
        self.mock_brl_balance = AccountBalanceData(
            available=Decimal("1000.0"),
            on_hold=Decimal("0.0"),
            symbol="BRL1",
            total=Decimal("1000.0")
        )
        
        self.mock_btc_balance = AccountBalanceData(
            available=Decimal("0.01"),
            on_hold=Decimal("0.0"),
            symbol="BTC",
            total=Decimal("0.01")
        )

    def test_get_balance_brl(self):
        """Testa obtenção de saldo BRL"""
        self.mock_api.get_accounts.return_value = [self.mock_account_data]
        self.mock_api.get_account_balance.return_value = [self.mock_brl_balance]
        
        account_id, balance = self.account.get_balance("BRL1")
        
        self.assertEqual(account_id, "brl-account-id")
        self.assertEqual(balance, Decimal("1000.0"))

    def test_get_balance_btc(self):
        """Testa obtenção de saldo BTC"""
        self.mock_api.get_accounts.return_value = [self.mock_btc_account_data]
        self.mock_api.get_account_balance.return_value = [self.mock_btc_balance]
        
        account_id, balance = self.account.get_balance("BTC")
        
        self.assertEqual(account_id, "btc-account-id")
        self.assertEqual(balance, Decimal("0.01"))

    def test_can_buy_without_position(self):
        """Testa se pode comprar quando não tem posição"""
        self.mock_api.get_accounts.return_value = [self.mock_account_data]
        self.mock_api.get_account_balance.return_value = [self.mock_brl_balance]
        
        self.assertTrue(self.account.can_buy())

    def test_can_buy_with_long_position(self):
        """Testa se não pode comprar quando já tem posição long"""
        # Criar posição long
        self.account.current_position = Position(
            symbol="BTC-BRL",
            side="long",
            quantity=Decimal("0.001"),
            entry_price=Decimal("100000.0"),
            entry_time=datetime.now()
        )
        
        self.assertFalse(self.account.can_buy())

    def test_can_buy_insufficient_balance(self):
        """Testa se não pode comprar com saldo insuficiente"""
        low_balance = AccountBalanceData(
            available=Decimal("10.0"),  # Menos que o mínimo de 50
            on_hold=Decimal("0.0"),
            symbol="BRL1",
            total=Decimal("10.0")
        )
        
        self.mock_api.get_accounts.return_value = [self.mock_account_data]
        self.mock_api.get_account_balance.return_value = [low_balance]
        
        self.assertFalse(self.account.can_buy())

    def test_can_sell_with_long_position(self):
        """Testa se pode vender quando tem posição long"""
        # Criar posição long
        self.account.current_position = Position(
            symbol="BTC-BRL",
            side="long",
            quantity=Decimal("0.001"),
            entry_price=Decimal("100000.0"),
            entry_time=datetime.now()
        )
        
        self.mock_api.get_accounts.return_value = [self.mock_btc_account_data]
        self.mock_api.get_account_balance.return_value = [self.mock_btc_balance]
        
        self.assertTrue(self.account.can_sell())

    def test_can_sell_without_position(self):
        """Testa se não pode vender quando não tem posição"""
        self.assertFalse(self.account.can_sell())

    def test_execute_buy_order_success(self):
        """Testa execução bem-sucedida de ordem de compra"""
        self.mock_api.get_accounts.return_value = [self.mock_account_data]
        self.mock_api.get_account_balance.return_value = [self.mock_brl_balance]
        self.mock_api.place_order.return_value = "order-123"
        
        def mock_quantity_calculator(balance, price):
            return "0.001"
        
        price = Decimal("100000.0")
        success = self.account.execute_buy_order(price, mock_quantity_calculator)
        
        self.assertTrue(success)
        self.assertIsNotNone(self.account.current_position)
        self.assertEqual(self.account.current_position.side, "long")
        self.assertEqual(self.account.current_position.entry_price, price)

    def test_execute_sell_order_success(self):
        """Testa execução bem-sucedida de ordem de venda"""
        # Criar posição long primeiro
        entry_price = Decimal("100000.0")
        current_price = Decimal("110000.0")
        
        self.account.current_position = Position(
            symbol="BTC-BRL",
            side="long",
            quantity=Decimal("0.001"),
            entry_price=entry_price,
            entry_time=datetime.now(),
            current_price=current_price
        )
        
        self.mock_api.get_accounts.return_value = [self.mock_btc_account_data]
        self.mock_api.get_account_balance.return_value = [self.mock_btc_balance]
        self.mock_api.place_order.return_value = "order-456"
        
        success = self.account.execute_sell_order()
        
        self.assertTrue(success)
        self.assertIsNone(self.account.current_position)
        self.assertEqual(len(self.account.position_history), 1)
        
        # Verificar histórico
        history = self.account.position_history[0]
        self.assertEqual(history.entry_price, entry_price)
        self.assertEqual(history.exit_price, current_price)
        self.assertEqual(history.realized_pnl, Decimal("10.0"))  # (110000 - 100000) * 0.001

    def test_position_unrealized_pnl(self):
        """Testa cálculo de PnL não realizado"""
        position = Position(
            symbol="BTC-BRL",
            side="long",
            quantity=Decimal("0.001"),
            entry_price=Decimal("100000.0"),
            entry_time=datetime.now(),
            current_price=Decimal("110000.0")
        )
        
        expected_pnl = Decimal("10.0")  # (110000 - 100000) * 0.001
        self.assertEqual(position.unrealized_pnl, expected_pnl)

    def test_get_total_realized_pnl(self):
        """Testa cálculo de PnL total realizado"""
        # Adicionar algumas posições ao histórico
        self.account.position_history = [
            PositionHistory(
                symbol="BTC-BRL",
                side="long",
                quantity=Decimal("0.001"),
                entry_price=Decimal("100000.0"),
                exit_price=Decimal("110000.0"),
                entry_time=datetime.now(),
                exit_time=datetime.now(),
                realized_pnl=Decimal("10.0")
            ),
            PositionHistory(
                symbol="BTC-BRL",
                side="long",
                quantity=Decimal("0.002"),
                entry_price=Decimal("105000.0"),
                exit_price=Decimal("100000.0"),
                entry_time=datetime.now(),
                exit_time=datetime.now(),
                realized_pnl=Decimal("-10.0")
            )
        ]
        
        total_pnl = self.account.get_total_realized_pnl()
        self.assertEqual(total_pnl, Decimal("0.0"))  # 10.0 + (-10.0)


if __name__ == "__main__":
    unittest.main()
