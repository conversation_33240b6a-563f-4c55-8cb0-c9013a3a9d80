#!/usr/bin/env python3
"""
Teste simples do sistema de logging colorido
"""

import sys
import os

# Adicionar o diretório trader ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'trader'))

try:
    from trader.colored_logger import get_trading_logger, setup_colored_logging
    print("✅ Importação bem-sucedida!")
    
    # Teste básico
    trading_logger = get_trading_logger("TestBot")
    print("✅ TradingLogger criado!")
    
    # Teste de logs
    trading_logger.log_bot_start("BTC-BRL")
    trading_logger.log_price("BTC-BRL", 651244.00)
    trading_logger.log_buy_signal(651244.00)
    trading_logger.log_bot_stop()
    
    print("✅ Sistema de logging colorido funcionando!")
    
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
except Exception as e:
    print(f"❌ Erro: {e}")
