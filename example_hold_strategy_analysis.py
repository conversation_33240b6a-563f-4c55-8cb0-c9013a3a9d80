#!/usr/bin/env python3
"""
Exemplo demonstrando a análise de Hold Strategy no relatório de execução
"""

import time
from decimal import Decimal
from datetime import datetime
from trader.colored_logger import get_trading_logger

def simulate_hold_strategy_analysis():
    """Simula um cenário para demonstrar a análise de Hold Strategy"""
    
    print("🔍 Demonstração da Análise de Hold Strategy\n")
    
    # Simular dados de uma execução
    print("=== Cenário Simulado ===")
    print("📌 Primeira posição: 0.00153846 BTC @ R$ 650,000.00")
    print("💰 Preço inicial: R$ 650,000.00")
    print("💰 Preço final: R$ 680,000.00")
    print("📊 Bot fez 3 operações com PnL total de R$ 15.50")
    print()
    
    # Calcular Hold Strategy
    first_position_entry_price = Decimal("650000.00")
    first_position_quantity = Decimal("0.00153846")
    final_price = Decimal("680000.00")
    actual_bot_pnl = Decimal("15.50")
    
    # PnL se tivesse mantido a primeira posição
    hold_pnl = (final_price - first_position_entry_price) * first_position_quantity
    
    # Diferença entre as estratégias
    difference = hold_pnl - actual_bot_pnl
    
    # Percentuais de retorno
    investment = first_position_entry_price * first_position_quantity
    hold_return_pct = (hold_pnl / investment) * 100
    actual_return_pct = (actual_bot_pnl / investment) * 100
    
    print("🔍 ===== ANÁLISE HOLD STRATEGY =====")
    print(f"📌 Primeira posição: {first_position_quantity:.8f} @ R$ {first_position_entry_price:.2f}")
    print(f"💰 Preço inicial: R$ {first_position_entry_price:.2f}")
    print(f"💰 Preço final: R$ {final_price:.2f}")
    
    if hold_pnl > 0:
        print(f"💰 PnL se tivesse mantido (HOLD): R$ {hold_pnl:.2f} ({hold_return_pct:+.2f}%)")
    else:
        print(f"💸 PnL se tivesse mantido (HOLD): R$ {hold_pnl:.2f} ({hold_return_pct:+.2f}%)")
        
    if actual_bot_pnl > 0:
        print(f"💰 PnL real do bot (TRADING): R$ {actual_bot_pnl:.2f} ({actual_return_pct:+.2f}%)")
    else:
        print(f"💸 PnL real do bot (TRADING): R$ {actual_bot_pnl:.2f} ({actual_return_pct:+.2f}%)")
    
    # Comparação final
    if difference > 0:
        print(f"📈 HOLD teria sido MELHOR por R$ {difference:.2f}")
        print(f"💡 Estratégia de hold teria superado o trading em {abs(difference):.2f} reais")
    elif difference < 0:
        print(f"📉 TRADING foi MELHOR por R$ {abs(difference):.2f}")
        print(f"🎯 Bot superou a estratégia de hold em {abs(difference):.2f} reais")
    else:
        print(f"⚖️ EMPATE: Ambas estratégias tiveram o mesmo resultado")
        
    print("🔍 ================================")
    
    print("\n=== Diferentes Cenários ===")
    
    # Cenário 1: Hold melhor
    print("\n📊 Cenário 1: Mercado em alta forte")
    simulate_scenario("Hold Melhor", 
                     entry_price=Decimal("650000"), 
                     final_price=Decimal("750000"), 
                     quantity=Decimal("0.00153846"), 
                     bot_pnl=Decimal("25.50"))
    
    # Cenário 2: Trading melhor
    print("\n📊 Cenário 2: Mercado volátil")
    simulate_scenario("Trading Melhor", 
                     entry_price=Decimal("650000"), 
                     final_price=Decimal("655000"), 
                     quantity=Decimal("0.00153846"), 
                     bot_pnl=Decimal("45.80"))
    
    # Cenário 3: Mercado em queda
    print("\n📊 Cenário 3: Mercado em queda")
    simulate_scenario("Mercado Baixa", 
                     entry_price=Decimal("650000"), 
                     final_price=Decimal("620000"), 
                     quantity=Decimal("0.00153846"), 
                     bot_pnl=Decimal("-15.20"))

def simulate_scenario(name: str, entry_price: Decimal, final_price: Decimal, 
                     quantity: Decimal, bot_pnl: Decimal):
    """Simula um cenário específico"""
    
    hold_pnl = (final_price - entry_price) * quantity
    difference = hold_pnl - bot_pnl
    investment = entry_price * quantity
    hold_return_pct = (hold_pnl / investment) * 100
    bot_return_pct = (bot_pnl / investment) * 100
    
    print(f"💰 Preço: R$ {entry_price:.0f} → R$ {final_price:.0f}")
    print(f"📊 Hold PnL: R$ {hold_pnl:.2f} ({hold_return_pct:+.2f}%)")
    print(f"🤖 Bot PnL: R$ {bot_pnl:.2f} ({bot_return_pct:+.2f}%)")
    
    if difference > 0:
        print(f"🏆 Vencedor: HOLD (+R$ {difference:.2f})")
    elif difference < 0:
        print(f"🏆 Vencedor: TRADING (+R$ {abs(difference):.2f})")
    else:
        print(f"🤝 Empate")

def show_implementation_details():
    """Mostra detalhes da implementação"""
    
    print("\n🔧 ===== DETALHES DA IMPLEMENTAÇÃO =====")
    print("✅ Rastreamento automático da primeira posição")
    print("✅ Registro do preço de entrada e quantidade")
    print("✅ Atualização contínua do preço final")
    print("✅ Cálculo automático no relatório de execução")
    print("✅ Comparação com PnL real do bot")
    print("✅ Exibição de percentuais de retorno")
    print("✅ Logs coloridos baseados no resultado")
    print()
    
    print("📝 Como funciona:")
    print("1. 📌 Na primeira compra, registra preço e quantidade")
    print("2. 💰 A cada iteração, atualiza o preço final")
    print("3. 📊 No relatório final, calcula PnL do hold")
    print("4. 🔍 Compara com PnL real e mostra diferença")
    print("5. 🎯 Indica qual estratégia teria sido melhor")
    print()
    
    print("💡 Benefícios:")
    print("• Análise objetiva da performance do bot")
    print("• Comparação com estratégia passiva")
    print("• Insights para melhoria da estratégia")
    print("• Validação da eficácia do trading ativo")

if __name__ == "__main__":
    try:
        simulate_hold_strategy_analysis()
        show_implementation_details()
        
        print("\n✨ Demonstração concluída!")
        print("💡 Esta análise agora aparece automaticamente no relatório do bot!")
        
    except Exception as e:
        print(f"\n❌ Erro na demonstração: {e}")
